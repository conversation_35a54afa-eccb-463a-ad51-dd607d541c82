import type { Project } from "@/services/main/projectMainService.ts";

import { useMemo } from "react";
import { Button } from "@heroui/button";
import { Avatar } from "@heroui/react";
import { ArrowLeftRight, FolderCog, Phone } from "lucide-react";
import { useNavigate } from "react-router-dom";

import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";
import { resetSidebarState } from "@/store/uiSlice.ts";
import { resetChat } from "@/store/messageSlice.ts";
import { resetChats } from "@/store/chatSlice.ts";
import { getInitials } from "@/utils/stringUtils.ts";

interface ActiveProjectPanelProps {
  project: Project;
}

export default function ActiveProjectPanel({
  project,
}: ActiveProjectPanelProps) {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { isMainMenuExpanded } = useAppSelector((state) => state.ui);

  const projectName = project?.projectName || "Unnamed Project";
  const projectInitials = useMemo(() => {
    const initials = getInitials(projectName);

    return initials || "PR";
  }, [projectName]);

  const projectDescription = project.description.trim();
  const providerRaw = project.whatsappCredentials?.provider ?? "";
  const phoneNumberRaw = project.whatsappCredentials?.phoneNumber ?? "";
  const providerLabel = providerRaw
    ? providerRaw
        .split(/[\s_-]+/)
        .filter(Boolean)
        .map(
          (segment) =>
            `${segment.charAt(0).toUpperCase()}${segment.slice(1).toLowerCase()}`,
        )
        .join(" ")
    : "";
  const formattedPhoneNumber = phoneNumberRaw.trim();

  const handleProjectSwitch = () => {
    navigate("/");
    dispatch(resetSidebarState());
    dispatch(resetChat());
    dispatch(resetChats());
  };

  if (!isMainMenuExpanded) {
    return null;
  }

  return (
    <div className="border-b border-gray-200 dark:border-gray-600 px-3 py-4">
      <div className="rounded-lg border border-gray-200/80 bg-gray-50 p-3 shadow-sm dark:border-gray-600/70 dark:bg-gray-800">
        <div className="flex items-start gap-3">
          <Avatar
            aria-label={`Active project ${projectName}`}
            className="h-11 w-11 shrink-0 bg-[color:var(--color-primary)] text-sm font-semibold uppercase text-white shadow-md"
            name={projectInitials}
            size="md"
          />
          <div className="min-w-0">
            <p className="text-[11px] font-semibold uppercase tracking-[0.2em] text-gray-500 dark:text-gray-400">
              Active Project
            </p>
            <p className="mt-1 text-sm font-semibold text-gray-900 dark:text-white truncate">
              {projectName}
            </p>
            {projectDescription && (
              <p className="mt-1 text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
                {projectDescription}
              </p>
            )}
          </div>
        </div>

        <div className="mt-4 flex flex-wrap items-center justify-between gap-3">
          <div className="flex flex-wrap items-center gap-2 text-[11px] text-gray-500 dark:text-gray-400">
            {providerLabel ? (
              <span className="inline-flex items-center gap-1 rounded-full bg-[color:var(--color-primary)]/[0.1] px-2 py-0.5 font-medium text-[color:var(--color-primary)] dark:bg-[color:var(--color-primary)]/[0.15] dark:text-[color:var(--color-primary)]">
                <FolderCog className="h-3.5 w-3.5" />
                {providerLabel}
              </span>
            ) : (
              <span className="inline-flex items-center gap-1 rounded-full bg-gray-500/10 px-2 py-0.5 font-medium text-gray-500 dark:bg-gray-700/60 dark:text-gray-300">
                <FolderCog className="h-3.5 w-3.5" />
                Provider unavailable
              </span>
            )}
            {formattedPhoneNumber ? (
              <span className="inline-flex items-center gap-1 rounded-full bg-emerald-500/10 px-2 py-0.5 font-medium text-emerald-600 dark:bg-emerald-500/15 dark:text-emerald-300">
                <Phone className="h-3.5 w-3.5" />
                {formattedPhoneNumber}
              </span>
            ) : (
              <span className="inline-flex items-center gap-1 rounded-full bg-amber-500/10 px-2 py-0.5 font-medium text-amber-600 dark:bg-amber-500/15 dark:text-amber-300">
                <Phone className="h-3.5 w-3.5" />
                No phone linked
              </span>
            )}
          </div>

          <Button
            className="shrink-0 font-medium bg-[color:var(--color-primary)] text-white hover:bg-[color:var(--color-primary-hover)]"
            size="sm"
            startContent={<ArrowLeftRight className="h-4 w-4" />}
            variant="flat"
            onPress={handleProjectSwitch}
          >
            Switch Project
          </Button>
        </div>
      </div>
    </div>
  );
}
