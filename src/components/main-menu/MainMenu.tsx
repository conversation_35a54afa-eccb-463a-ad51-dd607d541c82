import type { MenuItemConfig } from "@/types";

import { useCallback, useEffect, useRef, useState } from "react";
import { Button } from "@heroui/button";
import {
  ChevronDown,
  FolderCog,
  Home,
  LogOut,
  Menu,
  MessageSquare,
  Tags,
  Users,
  UserCog,
  X,
} from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";

import ActiveProjectPanel from "./ActiveProjectPanel.tsx";

import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";
import { setMainMenuExpanded, toggleMainMenu } from "@/store/uiSlice.ts";
import DarkModeToggle from "@/components/theme/DarkModeToggle.tsx";
import { useAuth } from "@/components/providers/AuthProvider.tsx";
import { Project } from "@/services/main/projectMainService.ts";

const MainMenu = (props: { project: Project }) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { isMainMenuExpanded, screenSize } = useAppSelector(
    (state) => state.ui,
  );
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [isBackdropVisible, setIsBackdropVisible] = useState(false);
  const [isBackdropOpaque, setIsBackdropOpaque] = useState(false);
  const [isAccountMenuOpen, setIsAccountMenuOpen] = useState(false);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const accountMenuRef = useRef<HTMLDivElement>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const leaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const auth = useAuth();

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
      if (leaveTimeoutRef.current) {
        clearTimeout(leaveTimeoutRef.current);
      }
    };
  }, []);

  // Handle Escape key press to close the menu
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isMainMenuExpanded) {
        dispatch(setMainMenuExpanded(false));
      }
    };

    // Add event listener when menu is expanded
    if (isMainMenuExpanded) {
      document.addEventListener("keydown", handleEscapeKey);
    }

    // Cleanup event listener
    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
    };
  }, [isMainMenuExpanded, dispatch]);

  // Handle outside clicks to close the menu on mobile and project dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close account menu if clicking outside
      if (isAccountMenuOpen && accountMenuRef.current) {
        if (!accountMenuRef.current.contains(event.target as Node)) {
          setIsAccountMenuOpen(false);
        }
      }

      // Only apply this behavior on mobile devices when the menu is expanded
      if (isMainMenuExpanded && screenSize === "mobile" && sidebarRef.current) {
        // Check if the click was outside the sidebar
        if (!sidebarRef.current.contains(event.target as Node)) {
          // Clear any pending timeouts when clicking outside
          if (hoverTimeoutRef.current) {
            clearTimeout(hoverTimeoutRef.current);
            hoverTimeoutRef.current = null;
          }
          if (leaveTimeoutRef.current) {
            clearTimeout(leaveTimeoutRef.current);
            leaveTimeoutRef.current = null;
          }
          dispatch(setMainMenuExpanded(false));
        }
      }
    };

    // Add event listener when menu is expanded on mobile or dropdown is open
    if ((isMainMenuExpanded && screenSize === "mobile") || isAccountMenuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    // Cleanup event listener
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMainMenuExpanded, screenSize, dispatch, isAccountMenuOpen]);

  // Handle backdrop visibility and opacity transitions
  useEffect(() => {
    if (isMainMenuExpanded && screenSize === "mobile") {
      // Show backdrop immediately when expanding
      setIsBackdropVisible(true);
      // Set opacity after a short delay to trigger fade-in
      setTimeout(() => setIsBackdropOpaque(true), 10);
    } else {
      // Start fade-out transition
      setIsBackdropOpaque(false);
      // Hide backdrop after transition completes
      setTimeout(() => setIsBackdropVisible(false), 300);
    }
  }, [isMainMenuExpanded, screenSize]);

  // Navigation handler
  const handleNavigation = (path: string) => {
    navigate(path);
    // Close menu on mobile after navigation
    if (screenSize === "mobile") {
      dispatch(setMainMenuExpanded(false));
    }
    setIsAccountMenuOpen(false);
  };

  const handleAccountSettings = () => {
    handleNavigation("/settings/account");
  };

  const handleLogout = async () => {
    setIsAccountMenuOpen(false);
    await auth.logout();
    if (screenSize === "mobile") {
      dispatch(setMainMenuExpanded(false));
    }
  };

  useEffect(() => {
    if (!isMainMenuExpanded) {
      setIsAccountMenuOpen(false);
    }
  }, [isMainMenuExpanded]);

  // Menu configuration
  const menuItems: MenuItemConfig[] = [
    {
      id: "home",
      label: "Home",
      icon: Home,
      isActive: location.pathname === `/project/${props.project.uid}/dashboard`,
      onClick: () =>
        handleNavigation(`/project/${props.project.uid}/dashboard`),
      hidden: true,
    },
    {
      id: "chats",
      label: "Chats",
      icon: MessageSquare,
      isActive: location.pathname === `/project/${props.project.uid}/chats`,
      onClick: () => handleNavigation(`/project/${props.project.uid}/chats`),
    },
    {
      id: "contacts",
      label: "Contacts",
      icon: Users,
      isActive: location.pathname === `/project/${props.project.uid}/contacts`,
      onClick: () => handleNavigation(`/project/${props.project.uid}/contacts`),
      hidden: true,
    },
    {
      id: "labels",
      label: "Labels",
      icon: Tags,
      isActive: location.pathname === `/project/${props.project.uid}/labels`,
      onClick: () => handleNavigation(`/project/${props.project.uid}/labels`),
    },
    {
      id: "project-settings",
      label: "Project Settings",
      icon: FolderCog,
      isActive: location.pathname.startsWith(
        `/project/${props.project.uid}/settings`,
      ),
      onClick: () => handleNavigation(`/project/${props.project.uid}/settings`),
    },
  ];

  const handleToggle = () => {
    dispatch(toggleMainMenu());
  };

  // Debounced hover handlers to prevent rapid state changes
  const handleMouseEnter = useCallback(() => {
    if (screenSize === "mobile") return;

    // Clear any pending leave timeout
    if (leaveTimeoutRef.current) {
      clearTimeout(leaveTimeoutRef.current);
      leaveTimeoutRef.current = null;
    }

    // Only expand if not already expanded, with a small delay to prevent flicker
    if (!isMainMenuExpanded) {
      hoverTimeoutRef.current = setTimeout(() => {
        dispatch(setMainMenuExpanded(true));
      }, 150); // 150ms delay to prevent accidental expansion
    }
  }, [screenSize, isMainMenuExpanded, dispatch]);

  const handleMouseLeave = useCallback(() => {
    if (screenSize === "mobile") return;

    // Clear any pending enter timeout
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }

    // Only collapse if currently expanded, with a delay for better UX
    if (isMainMenuExpanded) {
      leaveTimeoutRef.current = setTimeout(() => {
        dispatch(setMainMenuExpanded(false));
      }, 300); // 300ms delay to prevent immediate collapse
    }
  }, [screenSize, isMainMenuExpanded, dispatch]);

  // Optimize styling for true overlay behavior
  const baseWidth = "w-14"; // Collapsed width
  const expandedWidth = "w-56 lg:w-72"; // Expanded width - matches Recent Chats section on large screens
  const isMobile = screenSize === "mobile";
  const isOverlay = isMainMenuExpanded && isMobile;

  // Positioning classes - different for mobile vs desktop
  const positionClasses = "fixed left-0 top-0 h-full";
  const zIndex = isOverlay ? "z-[50]" : "z-[40]";

  // Width and transform for mobile overlay effect
  const width = isMainMenuExpanded ? expandedWidth : baseWidth;
  const transformClasses = isMobile
    ? `transform transition-transform duration-300 ease-in-out ${isMainMenuExpanded ? "translate-x-0" : "-translate-x-full"}`
    : "";

  // Smooth transitions with transform for better performance
  const transitionClasses = "transition-all duration-200 ease-out";
  // Highest elevation styling - strongest shadows and elevated background
  const shadowClasses = isMainMenuExpanded
    ? "shadow-2xl dark:shadow-black/40"
    : "shadow-xl dark:shadow-black/30";
  const accountMenuPositionClasses = isMainMenuExpanded
    ? "left-0 right-0"
    : "left-1/2 -translate-x-1/2 w-48 transform";

  return (
    <>
      {/* Main Sidebar */}
      <div
        ref={sidebarRef}
        className={`
          ${width} ${zIndex} ${positionClasses} ${transformClasses} ${transitionClasses} ${shadowClasses}
          bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-600
          flex flex-col backdrop-blur-sm
          ${screenSize === "mobile" ? "shadow-xl dark:shadow-black/40" : "shadow-2xl dark:shadow-black/50"}
        `}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* Header with Menu Toggle */}
        <div className="flex items-center justify-between p-2 border-b border-gray-200 dark:border-gray-600">
          <div
            className={`flex items-center space-x-2 ${!isMainMenuExpanded ? "justify-center w-full" : ""}`}
          >
            {isMainMenuExpanded && (
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-[color:var(--color-primary)] rounded-md flex items-center justify-center">
                  <span className="text-white font-bold text-xs">IL</span>
                </div>
                <span className="font-semibold text-gray-900 dark:text-white text-sm">
                  Ideal Lumatera
                </span>
              </div>
            )}
            {!isMainMenuExpanded && (
              <div className="w-6 h-6 bg-[color:var(--color-primary)] rounded-md flex items-center justify-center">
                <span className="text-white font-bold text-xs">IL</span>
              </div>
            )}
          </div>

          {/* Show burger menu when expanded, or close button on mobile when expanded */}
          {isMainMenuExpanded && (
            <Button
              isIconOnly
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 ml-2"
              size="sm"
              variant="light"
              onPress={handleToggle}
            >
              {isMobile ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          )}
        </div>

        <ActiveProjectPanel project={props.project} />

        {/* Navigation Items */}
        <nav className="flex-1 py-2 px-1">
          <ul className="space-y-1">
            {menuItems.map(
              (item) =>
                !item.hidden && (
                  <li key={item.id}>
                    <button
                      className={`
                    w-full flex items-center px-2 py-2 rounded-md text-left
                    transition-colors duration-200 relative group
                    ${
                      item.isActive
                        ? "bg-[color:var(--color-primary-disabled)] text-[color:var(--color-primary)] dark:bg-[color:var(--color-primary-disabled)] dark:text-[color:var(--color-primary)]"
                        : "text-gray-700 dark:text-gray-300 hover:bg-[color:var(--color-primary)]/10 dark:hover:bg-[color:var(--color-primary)]/20"
                    }
                  `}
                      onClick={item.onClick}
                      onMouseEnter={() => setHoveredItem(item.id)}
                      onMouseLeave={() => setHoveredItem(null)}
                    >
                      {/* Icon */}
                      <div className="flex-shrink-0 w-5 h-5">
                        <item.icon />
                      </div>

                      {/* Label (shown when expanded) */}
                      {isMainMenuExpanded && (
                        <span className="ml-2 font-medium text-sm">
                          {item.label}
                        </span>
                      )}

                      {/* Badge (shown when expanded and badge exists) */}
                      {isMainMenuExpanded && item.badge && (
                        <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center">
                          {item.badge}
                        </span>
                      )}

                      {/* Badge for collapsed state */}
                      {!isMainMenuExpanded && item.badge && (
                        <span className="absolute -top-0.5 -right-0.5 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                          {item.badge > 9 ? "9+" : item.badge}
                        </span>
                      )}

                      {/* Tooltip for collapsed state */}
                      {!isMainMenuExpanded && hoveredItem === item.id && (
                        <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white dark:text-gray-200 text-xs rounded whitespace-nowrap z-50">
                          {item.label}
                          {item.badge && (
                            <span className="ml-2 bg-red-500 rounded-full px-1.5 py-0.5 text-xs">
                              {item.badge}
                            </span>
                          )}
                        </div>
                      )}
                    </button>
                  </li>
                ),
            )}
          </ul>
        </nav>

        {/* User Profile Section */}
        <div
          ref={accountMenuRef}
          className="border-t border-gray-200 dark:border-gray-600 p-2 relative"
        >
          <button
            aria-expanded={isAccountMenuOpen}
            aria-haspopup="menu"
            className={`w-full flex items-center ${
              !isMainMenuExpanded ? "justify-center" : "space-x-2"
            } rounded-md px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200`}
            type="button"
            onClick={() => setIsAccountMenuOpen((prev) => !prev)}
          >
            <div className="w-8 h-8 bg-[color:var(--color-primary)] rounded-full flex items-center justify-center">
              <span className="text-white font-medium text-xs">
                {isMainMenuExpanded ? "JD" : "J"}
              </span>
            </div>

            {isMainMenuExpanded && (
              <div className="flex-1 min-w-0 text-left">
                <p className="font-medium text-gray-900 dark:text-white truncate text-sm">
                  {auth.user?.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  Online
                </p>
              </div>
            )}

            {isMainMenuExpanded && <ChevronDown className="h-4 w-4" />}
          </button>

          {isAccountMenuOpen && (
            <div
              className={`absolute bottom-full mb-2 ${accountMenuPositionClasses} z-50`}
            >
              <div className="overflow-hidden rounded-md border border-gray-200 bg-white py-1 shadow-lg dark:border-gray-600 dark:bg-gray-800">
                <button
                  className="flex w-full items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-[color:var(--color-primary)]/10 dark:text-gray-200 dark:hover:bg-[color:var(--color-primary)]/20"
                  type="button"
                  onClick={handleAccountSettings}
                >
                  <UserCog className="h-4 w-4" />
                  <span>Account Settings</span>
                </button>
                <button
                  className="flex w-full items-center space-x-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/30"
                  type="button"
                  onClick={handleLogout}
                >
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Dark Mode Toggle */}
        {isMainMenuExpanded && (
          <div className="border-t border-gray-200 dark:border-gray-600 p-2">
            <DarkModeToggle />
          </div>
        )}
      </div>

      {/* Semi-transparent Backdrop for expanded state - only when overlaying */}
      {isBackdropVisible && (
        <div
          className={`fixed inset-0 bg-black/30 backdrop-blur-sm z-40 transition-all duration-300 ease-in-out ${
            isBackdropOpaque ? "opacity-100" : "opacity-0"
          }`}
          role="button"
          tabIndex={0}
          onClick={() => {
            // For accessibility, allow backdrop to close the menu
            dispatch(setMainMenuExpanded(false));
          }}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              dispatch(setMainMenuExpanded(false));
            }
          }}
        />
      )}
    </>
  );
};

export default MainMenu;
