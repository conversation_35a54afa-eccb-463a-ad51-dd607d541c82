import React, { createContext, useEffect } from "react";
import { FirebaseError } from "firebase/app";
import { signInWithEmailAndPassword, signOut } from "firebase/auth";

import { AuthProviderState } from "@/types/auth/auth.types.ts";
import { auth } from "@/services/firebase/firebase.ts";
import { resetSidebarState } from "@/store/uiSlice.ts";
import { resetChat } from "@/store/messageSlice.ts";
import { resetChats } from "@/store/chatSlice.ts";
import { useAppDispatch } from "@/store/hooks.ts";

const authProviderState: AuthProviderState = {
  state: "none",
  user: null,
  errorMessages: null,
  login: async () => {},
  logout: async () => {},
};

const AuthContext = createContext<AuthProviderState>(authProviderState);

const getLoginErrorMessage = (error: unknown) => {
  if (error instanceof FirebaseError) {
    switch (error.code) {
      case "auth/invalid-email":
        return "Please provide a valid email address.";
      case "auth/user-disabled":
        return "This account has been disabled. Contact support for help.";
      case "auth/user-not-found":
        return "We couldn't find an account with that email.";
      case "auth/wrong-password":
        return "The password you entered is incorrect.";
      case "auth/invalid-credential":
        return "The credentials you entered are invalid.";
      case "auth/network-request-failed":
        return "Network error. Check your connection and try again.";
      case "auth/too-many-requests":
        return "Too many login attempts. Please wait and try again.";
      default:
        return error.message || "Unable to sign in right now. Try again later.";
    }
  }

  return "Unable to sign in right now. Try again later.";
};

interface AuthProviderProps {
  children: React.ReactNode;
}

const AuthProvider = (props: AuthProviderProps) => {
  const [user, setUser] = React.useState<AuthProviderState["user"]>(null);
  const [loginState, setLoginState] =
    React.useState<AuthProviderState["state"]>("checking");
  const [errorMessages, setErrorMessages] =
    React.useState<AuthProviderState["errorMessages"]>(null);
  const dispatch = useAppDispatch();

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {
        setUser({
          id: user.uid,
          name: user.displayName || "",
          email: user.email || "",
        });
        setLoginState("loggedIn");
      } else {
        setUser(null);
        setLoginState("none");
      }
    });

    return () => unsubscribe();
  }, []);

  const login = async (email: string, password: string) => {
    setLoginState("loggingIn");
    setErrorMessages(null);
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);

      setUser({
        id: result.user.uid,
        name: result.user.displayName || "",
        email: result.user.email || "",
      });
      setLoginState("loggedIn");
    } catch (error) {
      setLoginState("none");
      setErrorMessages(getLoginErrorMessage(error));
    }
  };

  const logout = async () => {
    await signOut(auth);
    setUser(null);
    setLoginState("none");
    setErrorMessages(null);
    dispatch(resetSidebarState());
    dispatch(resetChat());
    dispatch(resetChats());
  };

  return (
    <AuthContext.Provider
      value={{
        user: user,
        state: loginState,
        errorMessages,
        login,
        logout,
      }}
    >
      {props.children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => React.useContext(AuthContext);

export default AuthProvider;
