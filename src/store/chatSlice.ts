import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { ChatModel } from "@/types/firestore/chatModel";

interface ChatState {
  chats: ChatModel[];
  error: string | null;
  clientFilter: {
    searchQuery: string;
    label: {
      id: string;
      name: string;
    } | null;
  };
}

const initialState: ChatState = {
  chats: [],
  error: null,
  clientFilter: {
    searchQuery: "",
    label: null,
  },
};

export const chatSlice = createSlice({
  name: "chat",
  initialState,
  reducers: {
    setChats: (state, action: PayloadAction<ChatModel[]>) => {
      state.chats = action.payload;
    },

    addChat: (state, action: PayloadAction<ChatModel>) => {
      const existingIndex = state.chats.findIndex(
        (c) => c.id === action.payload.id,
      );

      if (existingIndex !== -1) {
        state.chats[existingIndex] = action.payload;
      } else {
        state.chats.unshift(action.payload);
      }
    },

    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.clientFilter.searchQuery = action.payload;
    },

    setLabel: (
      state,
      action: PayloadAction<{ id: string; name: string } | null>,
    ) => {
      state.clientFilter.label = action.payload;
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    resetChats: (state) => {
      state.chats = [];
      state.error = null;
      state.clientFilter.searchQuery = "";
      state.clientFilter.label = null;
    },
  },
});

export const {
  setChats,
  addChat,
  setSearchQuery,
  setError,
  resetChats,
  setLabel,
} = chatSlice.actions;

export default chatSlice.reducer;
