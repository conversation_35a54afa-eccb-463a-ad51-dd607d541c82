import { useHref, useNavigate } from "react-router-dom";
import { HeroUIProvider, ToastProvider } from "@heroui/react";
import { Provider as ReduxProvider } from "react-redux";
import React from "react";

import { store } from "./store";

import ThemeObserver from "@/components/theme/ThemeObserver";
import ModalProvider from "@/components/providers/ModalProvider";
import AuthProvider from "@/components/providers/AuthProvider";

export function Provider({ children }: { children: React.ReactNode }) {
  const navigate = useNavigate();

  return (
    <ReduxProvider store={store}>
      <HeroUIProvider navigate={navigate} useHref={useHref}>
        <ToastProvider
          maxVisibleToasts={3} // Limit visible toasts to prevent too many landmarks
          regionProps={{
            classNames: {
              base: "unique-toast-region", // Add unique identifier to the toast region
            },
          }}
        />
        <ThemeObserver />
        <AuthProvider>
          <ModalProvider>{children}</ModalProvider>
        </AuthProvider>
      </HeroUIProvider>
    </ReduxProvider>
  );
}
