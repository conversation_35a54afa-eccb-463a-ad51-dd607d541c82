/**
 * DefaultLayout Component
 *
 * A comprehensive layout component that serves as a scaffold/template for various pages
 * in the application. Based on ChatLayout structure with MainMenu sidebar and responsive behavior.
 *
 * Features:
 * - Responsive design with mobile-first approach
 * - MainMenu sidebar with overlay behavior
 * - Screen size detection and responsive state management
 * - Dark mode support
 * - Redux state management integration
 *
 * @example
 * ```tsx
 * <DefaultLayout>
 *   <ContentArea />
 * </DefaultLayout>
 * ```
 */

import type { ReactNode } from "react";

import { useEffect } from "react";
import { useOutletContext } from "react-router-dom";
import { Spinner } from "@heroui/react";

import { useAppDispatch, useAppSelector } from "../store/hooks";
import { setScreenSize } from "../store/uiSlice";
import MainMenu from "../components/main-menu/MainMenu.tsx";

import { Project } from "@/services/main/projectMainService.ts";

// Props interface for the DefaultLayout component
interface DefaultLayoutProps {
  children: ReactNode;
}

// Main DefaultLayout component
export default function DefaultLayout({ children }: DefaultLayoutProps) {
  const { project, projectLoadStatus } = useOutletContext<{
    project?: Project;
    projectLoadStatus: "none" | "loading" | "error" | "success";
  }>();
  const dispatch = useAppDispatch();
  const { visiblePanels, screenSize } = useAppSelector((state) => state.ui);

  // Screen size detection and responsive behavior
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      let newScreenSize: "mobile" | "tablet" | "desktop";

      if (width < 768) {
        newScreenSize = "mobile";
      } else if (width < 1024) {
        newScreenSize = "tablet";
      } else {
        newScreenSize = "desktop";
      }

      if (newScreenSize !== screenSize) {
        dispatch(setScreenSize(newScreenSize));
      }
    };

    // Initial check
    checkScreenSize();

    // Add resize listener
    window.addEventListener("resize", checkScreenSize);

    return () => {
      window.removeEventListener("resize", checkScreenSize);
    };
  }, [dispatch, screenSize]);

  return (
    <div className="relative h-screen bg-gray-50 dark:bg-gray-900 overflow-hidden">
      {/* Main Menu (Vertical Sidebar) - Always positioned absolutely to prevent layout shifts */}
      {visiblePanels.mainMenu && project && <MainMenu project={project} />}

      {/* Content Area - Children passed from parent component */}
      {projectLoadStatus === "loading" && (
        <div className="flex h-full items-center justify-center">
          <Spinner size="lg" />
        </div>
      )}
      {projectLoadStatus === "success" && children}
    </div>
  );
}

// Named export for the props interface
export type { DefaultLayoutProps };
