import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>u } from "lucide-react";
import { ReactNode } from "react";
import { useNavigate } from "react-router-dom";

import { setMainMenuExpanded } from "@/store/uiSlice.ts";
import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";
import HeaderLayout from "@/layouts/HeaderLayout.tsx";

interface SettingsPageLayoutProps {
  title: string;
  description: string;
  children: ReactNode;
}

const SettingsPageLayout = ({
  title,
  description,
  children,
}: SettingsPageLayoutProps) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { visiblePanels, screenSize } = useAppSelector((state) => state.ui);

  const leftMargin =
    visiblePanels.mainMenu && screenSize !== "mobile" ? "ml-14" : "ml-0";

  const handleBack = () => {
    navigate(-1);
  };

  const handleToggleMainMenu = () => {
    dispatch(setMainMenuExpanded(true));
  };

  return (
    <HeaderLayout>
      <div
        className={`flex h-full ${leftMargin} transition-all duration-200 ease-out`}
      >
        {screenSize === "mobile" && !visiblePanels.mainMenu && (
          <button
            aria-label="Open menu"
            className="fixed top-4 left-4 z-30 p-2 rounded-md bg-white dark:bg-gray-800 shadow-md hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
            onClick={handleToggleMainMenu}
          >
            <Menu className="w-6 h-6 text-gray-800 dark:text-gray-20" />
          </button>
        )}

        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-6 space-y-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <button
                  aria-label="Go back"
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  onClick={handleBack}
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {title}
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {description}
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-6">{children}</div>
          </div>
        </div>
      </div>
    </HeaderLayout>
  );
};

export default SettingsPageLayout;
