import { <PERSON><PERSON><PERSON><PERSON>, Edit, Tag, Trash2, Menu } from "lucide-react";
import { useNavigate, useOutletContext } from "react-router-dom";
import {
  Button,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { useEffect, useState } from "react";

import { LabelCard } from "./LabelCard";
import LabelFormModal from "./LabelFormModal";

import { useModal } from "@/hooks/useModal.ts";
import DefaultLayout from "@/layouts/default";
import { setMainMenuExpanded } from "@/store/uiSlice";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { LabelModel } from "@/types/firestore/labelModel";
import { formatDate } from "@/utils/dateUtils";
import { deleteExistingLabel, fetchAllLabels } from "@/store/labelsSlice";

interface Project {
  uid: string;
  projectName: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  whatsappCredentials: any;
  ownerUserUid: string;
  userAccess: any[];
}

export default function Labels() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { visiblePanels, screenSize } = useAppSelector((state) => state.ui);
  const { labels, loading, error } = useAppSelector((state) => state.labels);
  const { project } = useOutletContext<{ project?: Project }>();

  const leftMargin =
    visiblePanels.mainMenu && screenSize !== "mobile" ? "ml-14" : "ml-0";

  const handleBack = () => {
    navigate(-1);
  };

  const handleToggleMainMenu = () => {
    dispatch(setMainMenuExpanded(true));
  };
  const { showConfirm, showSuccess, showError } = useModal();

  // State for managing the label form modal
  const [isLabelModalOpen, setIsLabelModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<"create" | "edit">("create");
  const [currentLabel, setCurrentLabel] = useState<LabelModel | null>(null);

  // Fetch labels when component mounts and project is available
  useEffect(() => {
    if (project) {
      dispatch(fetchAllLabels({ projectId: project.uid }));
    }
  }, [dispatch, project]);

  // Handle error messages
  useEffect(() => {
    if (error) {
      showError({
        title: "Error",
        message: error,
        confirmText: "OK",
      });
    }
  }, [error, showError]);

  // Handler for adding a new label
  const handleAddLabel = () => {
    setModalMode("create");
    setCurrentLabel(null);
    setIsLabelModalOpen(true);
  };

  // Handler for editing an existing label
  const handleEdit = (label: LabelModel) => {
    setModalMode("edit");
    setCurrentLabel(label);
    setIsLabelModalOpen(true);
  };

  const handleDelete = async (label: LabelModel) => {
    if (!project) {
      showError({
        title: "Project Not Found",
        message: "Cannot delete label without an active project.",
        confirmText: "OK",
      });

      return;
    }

    showConfirm({
      title: "Delete Label",
      message: `Are you sure you want to delete label "${label.name}"?`,
      onConfirm: async () => {
        try {
          await dispatch(
            deleteExistingLabel({
              projectId: project.uid,
              labelId: label.id,
            }),
          ).unwrap();

          showSuccess({
            title: "Success",
            message: `Label "${label.name}" has been deleted.`,
            confirmText: "OK",
          });
        } catch {
          // Log error for debugging
          // In production, consider using a proper error tracking service
          showError({
            title: "Failed to Delete Label",
            message: `An error occurred while deleting label "${label.name}". Please try again.`,
            confirmText: "OK",
          });
        }
      },
      onCancel: () => {
        // Label deletion cancelled
      },
      confirmText: "Hapus",
      cancelText: "Batal",
    });
  };

  return (
    <DefaultLayout>
      <div
        className={`flex h-full ${leftMargin} transition-all duration-200 ease-out`}
      >
        {screenSize === "mobile" && !visiblePanels.mainMenu && (
          <button
            aria-label="Open menu"
            className="fixed top-4 left-4 z-30 p-2 rounded-md bg-white dark:bg-gray-800 shadow-md hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
            onClick={handleToggleMainMenu}
          >
            <Menu className="w-6 h-6 text-gray-800 dark:text-gray-20" />
          </button>
        )}

        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-6 space-y-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <button
                  aria-label="Go back"
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  onClick={handleBack}
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Labels
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Organize your chats with labels to categorize and filter
                    them effectively.
                  </p>
                </div>
              </div>
            </div>

            {/* Labels Table - shown on medium and larger screens */}
            <div className="rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 hidden md:block">
              <div className="flex items-center justify-between p-4">
                <h3 className="text-md font-medium text-gray-900 dark:text-gray-200">
                  Labels List
                </h3>
                <button
                  className="rounded-md bg-[color:var(--color-primary)] px-3 py-1.5 text-sm font-medium text-white hover:bg-[color:var(--color-primary-hover)] focus:outline-none focus:ring-2 focus:ring-[color:var(--color-primary)] focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                  disabled={loading}
                  onClick={handleAddLabel}
                >
                  {loading ? "Loading..." : "Add Label"}
                </button>
              </div>

              <Table
                removeWrapper
                aria-label="Labels table"
                className="px-4 pb-4"
              >
                <TableHeader>
                  <TableColumn className="text-left font-semibold text-gray-900 dark:text-gray-200">
                    ID
                  </TableColumn>
                  <TableColumn className="text-left font-semibold text-gray-900 dark:text-gray-200">
                    Name
                  </TableColumn>
                  <TableColumn className="text-left font-semibold text-gray-900 dark:text-gray-200">
                    Description
                  </TableColumn>
                  <TableColumn className="text-left font-semibold text-gray-90 dark:text-gray-200">
                    Created At
                  </TableColumn>
                  <TableColumn className="text-left font-semibold text-gray-90 dark:text-gray-200">
                    Updated At
                  </TableColumn>
                  <TableColumn className="text-left font-semibold text-gray-900 dark:text-gray-200">
                    Actions
                  </TableColumn>
                </TableHeader>
                <TableBody
                  emptyContent={
                    <div className="text-center py-12">
                      <Tag className="w-16 h-16 text-gray-30 mx-auto mb-4 dark:text-gray-400" />
                      <h3 className="text-xl font-semibold text-gray-800 mb-2 dark:text-gray-100">
                        No labels yet
                      </h3>
                      <p className="text-gray-500 dark:text-gray-400">
                        {loading
                          ? "Loading labels..."
                          : "Create your first label to get started"}
                      </p>
                    </div>
                  }
                  items={labels}
                >
                  {(label) => (
                    <TableRow
                      key={label.id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700/50"
                    >
                      <TableCell className="font-medium text-gray-900 dark:text-gray-100">
                        {label.id}
                      </TableCell>
                      <TableCell className="font-medium text-gray-900 dark:text-gray-100">
                        {label.name}
                      </TableCell>
                      <TableCell className="text-gray-600 dark:text-gray-300 max-w-xs truncate">
                        {label.description}
                      </TableCell>
                      <TableCell className="text-gray-600 dark:text-gray-300">
                        {formatDate(label.createdAt)}
                      </TableCell>
                      <TableCell className="text-gray-600 dark:text-gray-300">
                        {formatDate(label.updatedAt)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            isIconOnly
                            className="text-gray-500 hover:text-primary dark:text-gray-400 dark:hover:text-primary"
                            isDisabled={loading}
                            size="sm"
                            variant="light"
                            onPress={() => handleEdit(label)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            isIconOnly
                            className="text-gray-500 hover:text-danger dark:text-gray-400 dark:hover:text-danger"
                            isDisabled={loading}
                            size="sm"
                            variant="light"
                            onPress={() => handleDelete(label)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Labels Card Layout - shown on small screens */}
            <div className="md:hidden space-y-4">
              <div className="flex items-center justify-between px-4">
                <h3 className="text-md font-medium text-gray-900 dark:text-gray-20">
                  Labels List
                </h3>
                <button
                  className="rounded-md bg-[color:var(--color-primary)] px-3 py-1.5 text-sm font-medium text-white hover:bg-[color:var(--color-primary-hover)] focus:outline-none focus:ring-2 focus:ring-[color:var(--color-primary)] focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                  disabled={loading}
                  onClick={handleAddLabel}
                >
                  {loading ? "Loading..." : "Add Label"}
                </button>
              </div>

              {labels.length > 0 ? (
                <div className="space-y-4 px-4">
                  {labels.map((label) => (
                    <LabelCard key={label.id} label={label} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 px-4">
                  <Tag className="w-16 h-16 text-gray-30 mx-auto mb-4 dark:text-gray-400" />
                  <h3 className="text-xl font-semibold text-gray-800 mb-2 dark:text-gray-100">
                    {loading ? "Loading labels..." : "No labels yet"}
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    {loading
                      ? "Please wait..."
                      : "Create your first label to get started"}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Label Form Modal */}
      <LabelFormModal
        initialData={
          currentLabel
            ? {
                id: currentLabel.id,
                name: currentLabel.name,
                description: currentLabel.description,
              }
            : undefined
        }
        isOpen={isLabelModalOpen}
        mode={modalMode}
        onClose={() => setIsLabelModalOpen(false)}
      />
    </DefaultLayout>
  );
}
