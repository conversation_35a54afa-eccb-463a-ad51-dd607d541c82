import React, { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@heroui/button";
import { Textarea } from "@heroui/react";
import { Spinner } from "@heroui/spinner";
import { Paperclip, Send, X } from "lucide-react";

import { sendMessage } from "@/services/main/messageMainService.ts";

interface MessageInputProps {
  chatId: string;
  projectId: string;
}

const LoadingSpinner = () => <Spinner color="white" size="sm" />;

const MessageInput: React.FC<MessageInputProps> = ({ chatId, projectId }) => {
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<{
    message: string;
    type?: "network" | "api" | "auth" | "unknown";
  } | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSendMessage = async () => {
    if (message.trim() && !isLoading) {
      setIsLoading(true);
      setError(null); // Clear any previous errors
      try {
        // Send the message and wait for successful response
        await sendMessage(projectId, chatId, {
          type: "text",
          text: {
            body: message.trim(),
          },
        });

        // Only clear the message and refocus after successful sending
        setMessage("");

        // Focus back to textarea
        if (textareaRef.current) {
          textareaRef.current.focus();
        }
      } catch (error: any) {
        // Log error for debugging
        // In production, consider using a proper error tracking service

        // Don't reset the form on error - let the user keep their message

        // Determine error type and message
        let errorMessage = "Failed to send message";
        let errorType: "network" | "api" | "auth" | "unknown" = "unknown";

        if (error.code === "ERR_NETWORK" || !navigator.onLine) {
          errorMessage =
            "Network error. Please check your internet connection.";
          errorType = "network";
        } else if (error.response) {
          // API error
          if (error.response.status === 401 || error.response.status === 403) {
            errorMessage = "Authentication error. Please log in again.";
            errorType = "auth";
          } else if (error.response.status >= 500) {
            errorMessage = "Server error. Please try again later.";
            errorType = "api";
          } else if (error.response.status >= 400) {
            errorMessage =
              error.response.data?.message ||
              "Invalid request. Please try again.";
            errorType = "api";
          }
        } else if (error.request) {
          // Request was made but no response received
          errorMessage = "No response from server. Please try again.";
          errorType = "network";
        }

        setError({
          message: errorMessage,
          type: errorType,
        });
      } finally {
        // Always reset loading state
        setIsLoading(false);
      }
    }
  };

  const dismissError = () => {
    setError(null);
  };

  // Auto-dismiss error after 5 seconds
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (error) {
      timer = setTimeout(() => {
        setError(null);
      }, 5000);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [error]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      {/* Error Message */}
      {error && (
        <div className="mb-3 flex items-center justify-between bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 px-3 py-2 rounded-lg">
          <div className="flex items-center">
            <svg
              className="w-4 h-4 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                clipRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                fillRule="evenodd"
              />
            </svg>
            <span className="text-sm">{error.message}</span>
          </div>
          <Button
            isIconOnly
            className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 min-w-6 w-6 h-6"
            size="sm"
            variant="light"
            onPress={dismissError}
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      )}

      <div className="flex items-end space-x-2">
        {/* Attachment Button */}
        <Button
          isDisabled
          isIconOnly
          className="text-gray-300 dark:text-gray-600 flex-shrink-0 w-8 h-8"
          size="sm"
          variant="light"
        >
          <div className="w-4 h-4">
            <Paperclip className="w-4 h-4" />
          </div>
        </Button>

        {/* Message Input Container */}
        <div className="flex-1 flex items-end space-x-2">
          {/* Text Input */}
          <div className="flex-1 relative">
            <Textarea
              ref={textareaRef}
              classNames={{
                input: "text-sm leading-6 resize-none",
                inputWrapper:
                  "min-h-0 bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 rounded-2xl px-3 py-1.5",
              }}
              isDisabled={isLoading}
              maxRows={4}
              minRows={1}
              placeholder="Type a message..."
              rows={1}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
            />
          </div>

          {/* Send Button */}
          <Button
            isIconOnly
            className={`${isLoading ? "bg-blue-400" : "bg-blue-500 hover:bg-blue-600"} text-white rounded-full w-9 h-9 flex-shrink-0 transition-colors`}
            color="primary"
            isDisabled={!message.trim() || isLoading}
            size="sm"
            onPress={handleSendMessage}
          >
            <div className="w-5 h-5 flex items-center justify-center">
              {isLoading ? <LoadingSpinner /> : <Send className="w-5 h-5" />}
            </div>
          </Button>
        </div>
      </div>

      {/* Typing indicator placeholder */}
      {/* You can add a typing indicator here when someone else is typing */}
    </div>
  );
};

export default MessageInput;
