import type { Key } from "react";

import React, { memo } from "react";
import { MoreVertical } from "lucide-react";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
} from "@heroui/react";

interface MessageBubbleDropdownProps {
  direction: "in" | "out";
  className?: string;
  onAction?: (key: Key) => void;
}

const MessageBubbleDropdown: React.FC<MessageBubbleDropdownProps> = memo(
  ({ className = "", onAction }) => {
    const handleAction = (key: Key) => {
      if (!onAction) {
        return;
      }
      onAction(key);
    };

    return (
      <div className={className}>
        <Dropdown>
          <DropdownTrigger>
            <Button
              isIconOnly
              className={`w-6 h-6 min-w-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200`}
              size="sm"
              variant="light"
            >
              <MoreVertical className="w-4 h-4" />
            </Button>
          </DropdownTrigger>
          <DropdownMenu aria-label="Message actions" onAction={handleAction}>
            <DropdownItem
              key="copyMessageId"
              className="text-gray-700 dark:text-gray-300"
            >
              Copy Message ID
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
    );
  },
);

MessageBubbleDropdown.displayName = "MessageBubbleDropdown";

export default MessageBubbleDropdown;
