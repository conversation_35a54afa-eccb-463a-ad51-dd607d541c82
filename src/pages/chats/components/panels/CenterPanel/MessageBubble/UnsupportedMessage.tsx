import type { NormalizedMessage } from "../../../../../../types";

import { useState } from "react";
import { AlertTriangle } from "lucide-react";

interface UnsupportedMessageProps {
  message: NormalizedMessage;
}

const UnsupportedMessage = ({ message }: UnsupportedMessageProps) => {
  const [showDetails, setShowDetails] = useState(false);
  const errors = message.errors;

  const getErrorSummary = () => {
    if (!errors || errors.length === 0)
      return "This message format cannot be displayed.";
    if (errors.length === 1)
      return errors[0].error_data?.details || errors[0].message;

    return `${errors.length} error${errors.length > 1 ? "s" : ""} occurred while processing this message.`;
  };

  return (
    <div className="flex items-start space-x-2">
      <div className="flex-shrink-0 mt-0.5">
        <AlertTriangle className="h-3.5 w-3.5 text-amber-600 dark:text-amber-400" />
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <p className="text-xs font-medium text-amber-800 dark:text-amber-200">
            Unsupported message
          </p>
          {errors && errors.length > 0 && (
            <button
              className="text-xs text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300 transition-colors ml-2 flex-shrink-0"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? "Hide" : "Details"}
            </button>
          )}
        </div>
        <p className="text-xs text-amber-700 dark:text-amber-300 mt-0.5 opacity-90">
          {getErrorSummary()}
        </p>
        {showDetails && errors && errors.length > 0 && (
          <div className="mt-2 space-y-1.5 text-xs text-amber-600 dark:text-amber-400 border-t border-amber-200 dark:border-amber-800 pt-2">
            {errors.map(
              (
                error: {
                  code: number;
                  title: string;
                  message: string;
                  error_data?: { details: string };
                },
                index: number,
              ) => (
                <div key={index} className="space-y-0.5">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{error.title}</span>
                    <span className="opacity-75">({error.code})</span>
                  </div>
                  <p className="opacity-80">
                    {error.error_data?.details || error.message}
                  </p>
                </div>
              ),
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default UnsupportedMessage;
