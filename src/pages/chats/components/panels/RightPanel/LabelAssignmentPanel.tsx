import React, { useState } from "react";
import { Select, SelectItem } from "@heroui/select";
import { useOutletContext } from "react-router-dom";
import { addToast } from "@heroui/react";

import { ChatModel } from "../../../../../types/firestore/chatModel.ts";
import { LabelModel } from "../../../../../types/firestore/labelModel.ts";
import { useAppSelector } from "../../../../../store/hooks.ts";
import { updateChatLabel } from "../../../../../services/main/chatMainService.ts";
import { Project } from "../../../../../services/main/projectMainService.ts";

interface LabelAssignmentPanelProps {
  chat: ChatModel | null;
}

const LabelAssignmentPanel: React.FC<LabelAssignmentPanelProps> = ({
  chat,
}) => {
  const { labels } = useAppSelector((state) => state.labels);
  const { project } = useOutletContext<{ project?: Project }>();
  const [isUpdating, setIsUpdating] = useState(false);

  const handleLabelChange = async (
    keys: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    const value = keys.target.value;

    if (!chat || !project) {
      addToast({
        title: "Error: Missing Context",
        description:
          "Unable to update label - chat or project context not found",
        color: "danger",
      });

      return;
    }

    setIsUpdating(true);

    try {
      // If keys is empty (size === 0), we're removing the label
      if (!value) {
        await updateChatLabel(project.uid, chat.id, { labelId: null });
        addToast({
          title: "Label Removed",
          description: `Label successfully removed from chat: ${chat.name || chat.id}`,
          color: "success",
        });
      } else {
        await updateChatLabel(project.uid, chat.id, {
          labelId: value,
        });

        addToast({
          title: "Label Updated",
          description: `Label successfully updated to: ${labels.find((l) => l.id === value)?.name || value}`,
          color: "success",
        });
      }
    } catch (error) {
      addToast({
        title: "Update Failed",
        description: `Failed to update chat label: ${error instanceof Error ? error.message : "Unknown error occurred"}`,
        color: "danger",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="p-3 border-b border-divider">
      <h3 className="text-lg font-semibold mb-2">Assign Label</h3>
      <Select
        isClearable
        isDisabled={isUpdating}
        isLoading={isUpdating}
        label="Select a label"
        placeholder="Choose a label"
        selectedKeys={chat?.label?.id ? [chat.label.id] : []}
        selectionMode={"single"}
        value={chat?.label?.id}
        onChange={handleLabelChange}
      >
        {labels.map((label: LabelModel) => (
          <SelectItem key={label.id}>{label.name}</SelectItem>
        ))}
      </Select>
    </div>
  );
};

export default LabelAssignmentPanel;
