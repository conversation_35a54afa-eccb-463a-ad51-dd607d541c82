import React, { useCallback, useEffect, useState } from "react";
import { But<PERSON> } from "@heroui/button";
import { Select, SelectItem } from "@heroui/select";
import { useDispatch, useSelector } from "react-redux";
import { useOutletContext } from "react-router-dom";
import { SharedSelection } from "@heroui/react";

import { RootState } from "@/store";
import { fetchAllLabels } from "@/store/labelsSlice.ts";
import { Project } from "@/services/main/projectMainService.ts";

interface FilterDrawerProps {
  isOpen: boolean;
  onOpenChange: () => void;
  onFilterChange?: (selectedLabel: { id: string; name: string } | null) => void;
}

const FilterDrawer: React.FC<FilterDrawerProps> = ({
  isOpen,
  onOpenChange,
  onFilterChange,
}) => {
  const dispatch = useDispatch();
  const [selectedLabels, setSelectedLabels] = useState<string[]>([]);
  const { project } = useOutletContext<{ project?: Project }>();

  const projectId = project?.uid || "";

  const { labels, loading, error } = useSelector(
    (state: RootState) => state.labels,
  );

  const currentLabel = useSelector(
    (state: RootState) => state.chat.clientFilter.label,
  );

  useEffect(() => {
    if (isOpen && projectId) {
      dispatch(fetchAllLabels({ projectId }) as any);
    }
  }, [isOpen, projectId, dispatch]);

  // Set the initial selected label from Redux store when drawer opens
  useEffect(() => {
    if (isOpen && currentLabel) {
      setSelectedLabels([currentLabel.id]);
    } else if (isOpen && !currentLabel) {
      setSelectedLabels([]); // Reset to empty when no label is selected in store
    }
  }, [isOpen, currentLabel]);

  const handleLabelSelectionChange = (keys: SharedSelection) => {
    const selectedKey = Array.from(keys)[0];

    if (selectedKey) {
      setSelectedLabels([selectedKey.toString()]);
    } else {
      setSelectedLabels([]);
    }
  };

  const clearFilters = () => {
    setSelectedLabels([]);
    onFilterChange?.(null);
  };

  const applyFilters = useCallback(() => {
    const selectedKey = selectedLabels[0];

    if (selectedKey) {
      const selectedLabel = labels.find((label) => label.id === selectedKey);

      onFilterChange?.(
        selectedLabel
          ? { id: selectedLabel.id, name: selectedLabel.name }
          : null,
      );
    } else {
      onFilterChange?.(null);
    }
  }, [selectedLabels, labels, onFilterChange]);

  return (
    <div
      aria-label="Filter drawer"
      className={`absolute inset-y-0 left-0 z-1 w-full max-w-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-lg transform transition-transform duration-200 ease-out ${
        isOpen ? "translate-x-0" : "-translate-x-full"
      }`}
    >
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-base font-semibold text-gray-900 dark:text-white">
          Filters
        </h2>
        <Button size="sm" variant="light" onPress={onOpenChange}>
          Close
        </Button>
      </div>
      <div className="p-3">
        {loading && (
          <div className="flex items-center justify-center py-4">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Loading labels...
            </span>
          </div>
        )}

        {error && (
          <div className="flex items-center justify-center py-4">
            <span className="text-sm text-red-500 dark:text-red-400">
              Error loading labels: {error}
            </span>
          </div>
        )}

        {!loading && !error && (
          <Select
            label="Label"
            placeholder="Select a label"
            selectedKeys={selectedLabels}
            selectionMode="single"
            onSelectionChange={handleLabelSelectionChange}
          >
            {labels.map((label) => (
              <SelectItem key={label.id}>{label.name}</SelectItem>
            ))}
          </Select>
        )}

        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 flex gap-2">
          <Button fullWidth color="primary" onPress={applyFilters}>
            Apply Filters
          </Button>
          <Button fullWidth variant="light" onPress={clearFilters}>
            Clear Filters
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FilterDrawer;
