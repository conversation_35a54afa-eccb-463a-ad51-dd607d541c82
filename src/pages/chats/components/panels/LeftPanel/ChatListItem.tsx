import React, { memo } from "react";
import dayjs from "dayjs";

import { getInitials } from "../../../../../utils/stringUtils.ts";

import { useAppDispatch } from "@/store/hooks.ts";
import { openRightPanel } from "@/store/uiSlice.ts";
import { ChatModel } from "@/types/firestore/chatModel.ts";
import { Message } from "@/types/firestore/messageModel.ts";

interface ChatListItemProps {
  chat: ChatModel;
  isActive: boolean;
  onClick: () => void;
}
const formatTime = (timestamp: string): string => {
  const now = dayjs();
  const messageTime = dayjs(timestamp);
  const diffInHours = now.diff(messageTime, "hour");

  if (diffInHours < 24) {
    // Show time for today
    return messageTime.format("HH:mm");
  } else if (diffInHours < 48) {
    // Show "Yesterday" for yesterday
    return "Yesterday";
  } else if (diffInHours < 168) {
    // Show day name for this week
    return messageTime.format("ddd");
  } else {
    // Show date for older messages
    return messageTime.format("MMM D");
  }
};

const truncateMessage = (message: string, maxLength: number = 50): string => {
  if (message.length <= maxLength) return message;

  return message.substring(0, maxLength) + "...";
};

const getMessageContent = (message: Message<string> | undefined): string => {
  if (!message) return "";

  if (message.type === "text" && message.text?.body) {
    return message.text.body;
  }

  switch (message.type) {
    case "image":
      return "Image";
    case "audio":
      return "Audio";
    case "video":
      return "Video";
    case "document":
      return "Document";
    case "sticker":
      return "Sticker";
    case "location":
      return "Location";
    case "contacts":
      return "Contact";
    case "button":
      return message.button?.text ?? "Button";
    case "interactive":
      if (message.interactive?.type === "list_reply") {
        return message.interactive.list_reply?.title ?? "Interactive";
      }
      if (message.interactive?.type === "button_reply") {
        return message.interactive.button_reply?.title ?? "Interactive";
      }

      return "Interactive";
    case "reaction":
      return message.reaction?.emoji ?? "Reaction";
    case "system":
      return "System message";
    case "order":
      return "Order";
    case "unsupported":
    default:
      return "Message";
  }
};

const ChatListItem: React.FC<ChatListItemProps> = memo(
  ({ chat, isActive, onClick }) => {
    const dispatch = useAppDispatch();

    const handleChatClick = () => {
      onClick();
      // Open right panel when chat is selected (desktop only)
      dispatch(openRightPanel());
    };

    const getMessageTypeIcon = (type: Message<string>["type"]) => {
      switch (type) {
        case "image":
          return "📷 ";
        case "audio":
          return "🎵 ";
        case "video":
          return "🎥 ";
        case "document":
          return "📄 ";
        case "sticker":
          return "😀 ";
        case "location":
          return "📍 ";
        case "contacts":
          return "👤 ";
        case "button":
        case "interactive":
          return "🔘 ";
        case "reaction":
          return "😊 ";
        case "system":
          return "ℹ️ ";
        default:
          return "";
      }
    };

    const lastMessage = chat.lastMessage?.message;

    return (
      <div
        className={`
        flex items-center p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700
        transition-colors duration-150 relative group rounded
        ${isActive ? "bg-gradient-to-r from-indigo-50/60 to-transparent dark:from-indigo-900/20 dark:to-transparent shadow-sm border-r-2 border-indigo-500" : ""}
      `}
        role="button"
        tabIndex={0}
        onClick={handleChatClick}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            handleChatClick();
          }
        }}
      >
        {/* Avatar */}
        <div className="relative flex-shrink-0 mr-3">
          <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-600">
            <div className="w-full h-full flex items-center justify-center bg-blue-500 text-white font-medium text-base">
              {getInitials(chat.name)}
            </div>
          </div>
        </div>

        {/* Chat Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-0.5">
            <div className="flex items-center space-x-1.5 min-w-0">
              <h3
                className={`
              font-medium truncate text-sm
              ${isActive ? "text-blue-600 dark:text-blue-400" : "text-gray-900 dark:text-white"}
            `}
              >
                {chat.name}
              </h3>
            </div>

            <div className="flex items-center space-x-1.5 flex-shrink-0">
              {/* Time */}
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {lastMessage && formatTime(lastMessage.timestamp)}
              </span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            {/* Last Message */}
            <div className="flex-1 min-w-0">
              <p className="text-sm truncate text-gray-60 dark:text-gray-400">
                {lastMessage && (
                  <>
                    {getMessageTypeIcon(lastMessage.type)}
                    {chat.lastMessage?.direction === "out" && (
                      <span className="text-blue-600 dark:text-blue-400">
                        You:{" "}
                      </span>
                    )}
                    {truncateMessage(getMessageContent(lastMessage))}
                  </>
                )}
              </p>

              {/* Labels */}
              {chat.label && (
                <div className="flex items-center space-x-1 mt-1">
                  <span className="inline-block bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs px-2 py-0.5 rounded-full">
                    {chat.label.name}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  },
);

ChatListItem.displayName = "ChatListItem";

export default ChatListItem;
