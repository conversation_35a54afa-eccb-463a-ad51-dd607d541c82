import type { LeftPanelProps, Project } from "@/types";

import React, { useEffect, useMemo, useRef } from "react";
import { X, Menu, Search, Filter } from "lucide-react";
import { Input } from "@heroui/input";
import { Button } from "@heroui/button";
import { useDisclosure } from "@heroui/modal";
import { useOutletContext } from "react-router-dom";

import ChatListItem from "./ChatListItem.tsx";
import FilterDrawer from "./FilterDrawer.tsx";

import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";
import {
  setChats,
  setError,
  setLabel,
  setSearchQuery,
} from "@/store/chatSlice.ts";
import { setActiveChat } from "@/store/messageSlice.ts";
import { setMainMenuExpanded, showMobileChatView } from "@/store/uiSlice.ts";
import { ChatModel } from "@/types/firestore/chatModel.ts";
import subscribeToChats from "@/services/firebase/chatService.ts";

const LeftPanel: React.FC<LeftPanelProps> = ({ className }) => {
  const dispatch = useAppDispatch();
  const { chats, error, clientFilter } = useAppSelector((state) => state.chat);
  const { activeChat } = useAppSelector((state) => state.message);
  const { screenSize } = useAppSelector((state) => state.ui);
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  // Ref to keep track of the latest activeChat value
  const activeChatRef = useRef(activeChat);

  useEffect(() => {
    activeChatRef.current = activeChat;
  }, [activeChat]);
  const { project } = useOutletContext<{
    project: Project;
  }>();

  // Filter and search logic
  const filteredChats: ChatModel[] = useMemo(() => {
    if (!clientFilter.searchQuery && !clientFilter.label) {
      return chats;
    }

    let filtered = chats;

    // Apply search query filter
    if (clientFilter.searchQuery) {
      const query = clientFilter.searchQuery.toLowerCase();

      filtered = filtered.filter((chat) =>
        chat.phoneNumber.toLowerCase().includes(query),
      );
    }

    // Apply label filter (if a label is selected)
    if (clientFilter.label) {
      filtered = filtered.filter(
        (chat) => chat.label && chat.label.id === clientFilter.label?.id,
      );
    }

    return filtered;
  }, [chats, clientFilter]);

  const handleChatSelect = (chat: ChatModel) => {
    if (activeChat && activeChat.id === chat.id) {
      return;
    }

    dispatch(
      setActiveChat({
        chat,
        resetMessages: true,
      }),
    );

    // On mobile, switch to chat view
    if (screenSize === "mobile") {
      dispatch(showMobileChatView());
    }
  };

  const handleOpenMainMenu = () => {
    dispatch(setMainMenuExpanded(true));
  };

  const handleSearchChange = (value: string) => {
    dispatch(setSearchQuery(value));
  };

  useEffect(() => {
    const unsubscribe = subscribeToChats(
      project.uid,
      (chats) => {
        dispatch(setChats(chats));

        if (activeChatRef.current) {
          const activeChat = chats.find(
            (c) => c.id === activeChatRef.current?.id,
          );

          if (activeChat) {
            dispatch(
              setActiveChat({
                chat: activeChat,
              }),
            );
          }
        }
      },
      () => {
        dispatch(setError("Failed to load chats"));
      },
    );

    return () => {
      unsubscribe();
    };
  }, [project.uid]);

  const containerRef = useRef<HTMLDivElement>(null);

  // Active filters logic
  const activeFilters = useMemo(() => {
    const filters: Array<{
      type: "search" | "label";
      value: string;
      display: string;
    }> = [];

    // Add search filter if active
    if (clientFilter.searchQuery) {
      filters.push({
        type: "search",
        value: clientFilter.searchQuery,
        display: `Search: ${clientFilter.searchQuery}`,
      });
    }

    // Add label filter if active
    if (clientFilter.label) {
      filters.push({
        type: "label",
        value: clientFilter.label.name,
        display: `Label: ${clientFilter.label.name}`,
      });
    }

    return filters;
  }, [clientFilter]);

  // Handle filter removal
  const handleRemoveFilter = (type: "search" | "label") => {
    if (type === "search") {
      dispatch(setSearchQuery(""));
    } else if (type === "label") {
      dispatch(setLabel(null));
    }
  };

  // Handle filter change and close drawer
  const handleFilterChange = (
    selectedLabel: { id: string; name: string } | null,
  ) => {
    dispatch(setLabel(selectedLabel));
    onOpenChange(); // Close the filter drawer
  };

  // Render active filters
  const renderActiveFilters = () => {
    return activeFilters.map((filter, index) => {
      const truncatedText =
        filter.display.length > 25
          ? filter.display.substring(0, 22) + "..."
          : filter.display;

      return (
        <div
          key={index}
          className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-indigo-100 dark:bg-indigo-900/50 text-indigo-800 dark:text-indigo-200 border border-indigo-200 dark:border-indigo-700 shadow-sm flex-shrink-0 transition-all duration-200 ease-in-out hover:shadow-md"
        >
          <span className="mr-1" title={filter.display}>
            {truncatedText}
          </span>
          <button
            aria-label={`Remove filter: ${filter.display}`}
            className="ml-1 p-0.5 rounded-full text-gray-500 dark:text-gray-400 hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-600 dark:hover:text-red-400 transition-colors duration-150 ease-in-out"
            onClick={() => handleRemoveFilter(filter.type)}
          >
            <X className="w-3.5 h-3.5" />
          </button>
        </div>
      );
    });
  };

  return (
    <div
      ref={containerRef}
      className={`relative flex flex-col h-full bg-white dark:bg-gray-800 shadow-md dark:shadow-black/25 border-r border-gray-200 dark:border-gray-600 ${className}`}
    >
      {/* Header */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            {/* Mobile Hamburger Menu Button */}
            {screenSize === "mobile" && (
              <Button
                isIconOnly
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                size="sm"
                variant="light"
                onPress={handleOpenMainMenu}
              >
                <Menu className="w-5 h-5" />
              </Button>
            )}
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
              Chats
            </h1>
          </div>
          <div className="flex items-center space-x-1">
            <Button isIconOnly size="sm" variant="light" onPress={onOpen}>
              <Filter className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Input
            className="w-full"
            classNames={{
              input: "text-sm",
              inputWrapper:
                "bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600",
            }}
            placeholder="Search Phone Number"
            startContent={<Search className="w-5 h-5" />}
            type="text"
            value={clientFilter.searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
          />
        </div>
        {error && (
          <div className="mt-2 text-sm text-red-500 dark:text-red-400">
            {error}
          </div>
        )}

        {/* Active Filters */}
        <div className="relative mt-4">
          <div className="flex flex-nowrap gap-2 overflow-x-auto scroll-smooth transparent-scrollbar">
            {renderActiveFilters()}
          </div>
          {/* Solid color fade indicator for scroll */}
          <div className="absolute right-0 top-0 bottom-0 w-4 bg-gray-50 dark:bg-gray-700 pointer-events-none border-l border-gray-200 dark:border-gray-600" />
        </div>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {filteredChats.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-gray-500 dark:text-gray-400">
            <p className="text-sm">No phone numbers found</p>
            {clientFilter.searchQuery && (
              <Button
                className="mt-2"
                size="sm"
                variant="light"
                onPress={() => dispatch(setSearchQuery(""))}
              >
                Clear search
              </Button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-100 dark:divide-gray-700">
            {filteredChats.map((chat) => (
              <ChatListItem
                key={chat.id}
                chat={chat}
                isActive={activeChat ? chat.id === activeChat.id : false}
                onClick={() => handleChatSelect(chat)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Footer Stats */}
      <div className="p-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>{filteredChats.length} phone numbers</span>
        </div>
      </div>

      <FilterDrawer
        isOpen={isOpen}
        onFilterChange={handleFilterChange}
        onOpenChange={onOpenChange}
      />
    </div>
  );
};

export default LeftPanel;
