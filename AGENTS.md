# Repository Guidelines

## Project Structure & Module Organization

- Entry chain: `src/main.tsx` → `src/provider.tsx` → `src/App.tsx`; all routing flows through the provider helpers.
- Feature screens live in `src/pages/<feature>/`, including: `chats/`, `contacts/`, `dashboard/`, `labels/`, `login/`, `select-project/`, `settings/`, `signup/`.
- Component organization: feature-specific components in `src/components/<feature>/`, shared components in `src/components/auth/`, `src/components/chat/`, `src/components/route/`, `src/components/theme/`.
- Redux Toolkit setup is found in `src/store/` with slices: `chatSlice.ts`, `contactsSlice.ts`, `labelsSlice.ts`, `messageSlice.ts`, `uiSlice.ts`.
- Shared assets: Firestore models in `src/types/firestore/`, general types in `src/types/`, helpers in `src/utils/`, theme in `src/styles/`.
- Custom hooks in `src/hooks/` and layouts in `src/layouts/`.

## Build, Test, and Development Commands

- `npm run dev` — Vite dev server with HMR.
- `npm run build` — run `tsc` type checks, then create the production bundle.
- `npm run preview` — serve the built bundle for smoke tests.
- `npm run lint` / `npm run format` — apply ESLint (`--fix`) and Prettier before commits.
- `npm run test` — run Vitest tests.
- `npm run test:ui` — run Vitest with UI interface.
- `npm run test:run` — run Vitest tests once.

## Dependencies & Technology Stack

- **Frontend**: React 18.3.1 with TypeScript 5.6.3
- **Build Tool**: Vite 6.0.11 with tsconfig paths
- **Styling**: Tailwind CSS 4.1.11 with HeroUI components
- **State Management**: Redux Toolkit 2.9.0 with React Redux 9.2.0
- **Routing**: React Router DOM 6.23.0
- **Testing**: Vitest 3.2.4 with Testing Library and jsdom environment
- **Icons**: Heroicons, Lucide React, React Icons
- **Utilities**: clsx, tailwind-variants, dayjs, axios
- **Backend**: Firebase 12.2.1 integration
- **Animation**: Framer Motion 11.18.2

## Coding Style & Naming Conventions

- TypeScript strict mode enabled; favor functional React components and typed hooks.
- Import order: React → third-party → blank line → `@/` aliases (ESLint enforced).
- Naming: PascalCase components, camelCase hooks/utilities, SCREAMING_SNAKE_CASE constants.
- Styling: Tailwind v4 utilities and HeroUI props; use `tailwind-variants` before custom CSS.
- Formatting: Prettier (2-space indent, semicolons); ESLint enforces React, hooks, unused-imports, and import order rules.
- Path aliases: `@/*` maps to `./src/*` in tsconfig.json.

## Testing Guidelines

- Use Vitest with Testing Library assertions; jsdom environment configured.
- Test setup in `vitest.setup.ts` with proper mocks for window.matchMedia.
- Mirror filenames (`Component.test.tsx`) and keep specs focused on observable behavior.
- Mock Firebase and REST clients; prefer dependency injection over global mocks.
- Validate async flows remain serializable for Redux middleware and cover guarded routes (`src/components/route/`).
- Test UI components with proper accessibility checks.

## Code Quality & Linting

- ESLint configuration in `eslint.config.mjs` with React, TypeScript, and import rules.
- Key rules: no-unused-vars, unused-imports, react-hooks, jsx-a11y, import/order.
- Prettier integration for consistent formatting.
- TypeScript strict mode with noUnusedLocals and noUnusedParameters enabled.

## Commit & Pull Request Guidelines

- Follow Conventional Commits (`feat(chat): ...`, `fix(auth): ...`) as demonstrated in history; keep scope tight.
- PRs should explain intent, list key changes, reference issues, and attach UI screenshots when visuals change.
- Confirm lint, format, build, and relevant tests succeed; note any skipped steps and mitigation.
- Request reviewer sign-off for updates touching routing guards, state slices, or shared types.

## Environment & Security Notes

- Secrets live in `.env.local` with `VITE_` prefixes; never commit Firebase keys or project IDs.
- New services belong in `src/services/` and should follow existing patterns; document added env vars in the PR description.
- Firebase integration for authentication and Firestore database operations.
