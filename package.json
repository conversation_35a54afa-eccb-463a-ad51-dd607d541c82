{"name": "vite-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint --fix", "preview": "vite preview", "format": "prettier --write .", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest --run"}, "dependencies": {"@heroicons/react": "^2.2.0", "@heroui/button": "2.2.26", "@heroui/card": "^2.2.24", "@heroui/code": "2.2.20", "@heroui/dropdown": "2.3.26", "@heroui/input": "2.4.27", "@heroui/kbd": "2.2.21", "@heroui/link": "2.2.22", "@heroui/navbar": "2.2.24", "@heroui/react": "^2.8.4", "@heroui/snippet": "2.2.27", "@heroui/switch": "2.2.23", "@heroui/system": "2.4.22", "@heroui/theme": "2.4.22", "@heroui/use-theme": "2.1.10", "@react-aria/visually-hidden": "3.8.27", "@react-types/shared": "3.32.0", "@reduxjs/toolkit": "^2.9.0", "@tailwindcss/postcss": "4.1.11", "@tailwindcss/vite": "4.1.11", "axios": "^1.12.2", "clsx": "2.1.1", "dayjs": "^1.11.18", "firebase": "^12.2.1", "framer-motion": "11.18.2", "lucide-react": "^0.543.0", "react": "18.3.1", "react-dom": "18.3.1", "react-redux": "^9.2.0", "react-router-dom": "6.23.0", "tailwind-variants": "3.1.1", "tailwindcss": "4.1.11"}, "devDependencies": {"@eslint/compat": "1.2.8", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.25.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.31.1", "@typescript-eslint/parser": "8.31.1", "@vitejs/plugin-react": "4.7.0", "@vitest/ui": "^3.2.4", "eslint": "9.25.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-unused-imports": "4.1.4", "globals": "16.0.0", "jsdom": "^27.0.0", "postcss": "8.5.6", "prettier": "3.5.3", "typescript": "5.6.3", "vite": "6.0.11", "vite-tsconfig-paths": "5.1.4", "vitest": "^3.2.4"}}